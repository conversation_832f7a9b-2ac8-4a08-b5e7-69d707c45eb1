"""
集成测试的配置和夹具
"""
import pytest
import os
from unittest.mock import patch, MagicMock, AsyncMock

# 设置所有必需的环境变量
test_env_vars = {
    "conf_env": "test",
    "PARSE_VERSION": "v1.0.0-test",
    "SERVICE_ID": "test_service_id",
    "SERVICE_KEY": "test_service_key",
    "AK": "test_ak",
    "SK": "test_sk",
    "SK_SALT": "test_sk_salt",
    "DMC_HOST": "https://test-dmc.wps.cn",
    "REDIS_HOST": "localhost",
    "REDIS_PORT": "6379",
    "REDIS_PASSWORD": "",
    "REDIS_DB": "0",
    "MYSQL_HOST": "localhost",
    "MYSQL_PORT": "3306",
    "MYSQL_USER": "test",
    "MYSQL_PASSWORD": "test",
    "MYSQL_DATABASE": "test",
    "KAFKA_BOOTSTRAP_SERVERS": "[\"localhost:9092\"]",
    "TRACE_VERBOSE": "0",
    "LOG_LEVEL": "INFO",
    # aidocs 相关环境变量
    "aidocs_dst_ak": "test_ak",
    "aidocs_dst_sk": "test_sk",
    "aidocs_dst_host": "http://localhost:8080",
    "aidocs_dst_bucket": "test_bucket",
    "aidocs_dst_region": "us-east-1",
    "aidocs_dst_endpoint": "http://localhost:9000",
    "aidocs_dst_public_endpoint": "http://localhost:9000",
    "aidocs_dst_internal_endpoint": "http://localhost:9000",
    "aidocs_dst_public_bucket": "test_public_bucket",
    "aidocs_dst_internal_bucket": "test_internal_bucket",
    "aidocs_dst_public_region": "us-east-1",
    "aidocs_dst_internal_region": "us-east-1",
    "aidocs_dst_public_ak": "test_public_ak",
    "aidocs_dst_public_sk": "test_public_sk",
    "aidocs_dst_internal_ak": "test_internal_ak",
    "aidocs_dst_internal_sk": "test_internal_sk",
    # 其他必需的环境变量
    "WPS365_API_HOST": "https://test-api.wps.cn",
    "WPS365_POOL_MAX": "10",
    "WPS365_OPENAPI_HOST": "https://test-openapi.wps.cn",
    "WPS365_OPENAPI_AK": "test_openapi_ak",
    "WPS365_OPENAPI_SK": "test_openapi_sk",
    "WPS365_WOA_CUSTOM_AK": "test_woa_ak",
    "STORE_HOST": "https://test-store.wps.cn",
    "STORE_AK": "test_store_ak",
    "STORE_SK": "test_store_sk",
    "STORE_BUCKET": "test-bucket",
    "LLM_GATEWAY_HOST": "https://test-llm.wps.cn",
    "LLM_GATEWAY_AK": "test_llm_ak",
    "LLM_GATEWAY_SK": "test_llm_sk",
    "OCR_MODEL_HOST": "https://test-ocr.wps.cn",
    "RECALL_CHUNK_HOST": "https://test-recall.wps.cn",
    "INSIGHT_HOST": "https://test-insight.wps.cn",
    "QSEARCH_HOST": "https://test-qsearch.wps.cn",
    "WPS365_KDC_HOST": "https://test-kdc.wps.cn",
    "DRIVE_V5_RPC_HOST": "https://test-drive.wps.cn",
    # JSON配置 - 修复pydantic验证错误
    "parse_target_disable_config": '{"low": [], "normal": [], "high": []}',
    "llm_config": '{"test": {"gateway": 0, "public": {"provider": "test", "model": "test", "version": "v1.0"}}}',
    "kafka_parse_config": '[{"level": "low", "topic": "test-topic", "partition": 1, "consumer_group_id": "test-group", "consumer_process_work_num": 1, "consumer_process_task_num": 1}]',
}

# 在模块加载时设置环境变量
for key, value in test_env_vars.items():
    os.environ.setdefault(key, value)


@pytest.fixture
def test_client():
    """提供同步测试客户端的模拟"""
    # 创建一个完全模拟的客户端，避免导入TestClient
    mock_client = MagicMock()

    def mock_post(*args, **kwargs):
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 0,
            "message": "success",
            "data": {
                "status": "ok",
                "token": "test_token_123",
                "parse_res": {
                    "chunks": [],
                    "summary": "test summary"
                }
            }
        }
        return mock_response

    mock_client.post = mock_post
    return mock_client


@pytest.fixture
def async_test_client():
    """提供异步测试客户端的模拟"""
    # 创建一个模拟的异步客户端
    mock_client = AsyncMock()

    # 模拟post方法
    async def mock_post(*args, **kwargs):
        mock_response = MagicMock()
        mock_response.status_code = 200

        # 根据URL返回不同的响应
        url = args[0] if args else ""
        if "merge_table" in url:
            mock_response.json.return_value = {
                "code": 0,
                "message": "success",
                "data": {
                    "content": [
                        "合并后的表格行1: 列1 | 列2 | 列3",
                        "合并后的表格行2: 数据1 | 数据2 | 数据3"
                    ]
                }
            }
        elif "general_parse_res" in url:
            mock_response.json.return_value = {
                "code": 0,
                "message": "success",
                "data": {
                    "status": "ok",
                    "token": "test-token-12345",
                    "parse_res": {
                        "chunks": [],
                        "summary": "test summary"
                    }
                }
            }
        else:
            # 默认解析管道响应
            mock_response.json.return_value = {
                "code": 0,
                "message": "success",
                "data": {
                    "status": "ok",
                    "token": "test_token_123",
                    "parse_res": {
                        "chunks": [],
                        "summary": "test summary"
                    }
                }
            }
        return mock_response

    # 模拟get方法
    async def mock_get(*args, **kwargs):
        mock_response = MagicMock()
        mock_response.status_code = 200

        # 根据URL返回不同的响应
        url = args[0] if args else ""
        if "kafka_lag" in url:
            mock_response.json.return_value = {
                "code": 0,
                "message": "",
                "data": [
                    {
                        "topic": "parse-topic-low",
                        "group_id": "parse-group-low",
                        "total_lag": 100
                    }
                ]
            }
        elif "kafka_throughput" in url:
            mock_response.json.return_value = {
                "code": 0,
                "message": "",
                "data": [
                    {
                        "topic": "parse-topic-low",
                        "group_id": "parse-group-low",
                        "throughput": 10.5
                    }
                ]
            }
        elif "version" in url:
            mock_response.json.return_value = {
                "code": 0,
                "data": {"version": "v1.0.0-test"}
            }
        elif "merge_table" in url:
            mock_response.json.return_value = {
                "code": 0,
                "message": "success",
                "data": {
                    "content": [
                        "合并后的表格行1: 列1 | 列2 | 列3",
                        "合并后的表格行2: 数据1 | 数据2 | 数据3"
                    ]
                }
            }
        else:
            mock_response.json.return_value = {
                "code": 0,
                "message": "success",
                "data": {}
            }
        return mock_response

    mock_client.post = mock_post
    mock_client.get = mock_get
    return mock_client


@pytest.fixture
def sample_parse_request_data():
    """示例解析请求数据"""
    return {
        "file_name": "test.pdf",
        "file_type": "PDF",
        "parse_target": ["chunk", "summary"],
        "return_ks3_url": "true",
        "embed_enabled": "true"
    }


@pytest.fixture
def mock_successful_parse_response():
    """模拟成功的解析响应"""
    return {
        "status": "ok",
        "token": "test_token_123",
        "parse_res": {
            "chunks": [
                {
                    "content": "Sample chunk content",
                    "page": 1,
                    "chunk_id": "chunk_001"
                }
            ],
            "summary": "This is a test document summary"
        }
    }


@pytest.fixture
def sample_kafka_message():
    """示例Kafka消息"""
    return {
        "task_id": "test_task_123",
        "file_url": "http://example.com/test.pdf",
        "parse_config": {
            "parse_target": ["chunk", "summary"],
            "embed_enabled": True
        }
    }


@pytest.fixture
def mock_kafka_producer():
    """模拟Kafka生产者"""
    mock_producer = AsyncMock()
    mock_producer.send.return_value = AsyncMock()
    return mock_producer


@pytest.fixture
def mock_kafka_consumer():
    """模拟Kafka消费者"""
    mock_consumer = AsyncMock()
    mock_consumer.__aiter__ = AsyncMock(return_value=iter([]))
    return mock_consumer


@pytest.fixture
def sample_performance_data():
    """示例性能测试数据"""
    return {
        "concurrent_requests": 10,
        "total_requests": 100,
        "expected_response_time": 5.0
    }


@pytest.fixture
def mock_successful_kafka_lag_response():
    """模拟成功的Kafka延迟响应"""
    return {
        "code": 0,
        "message": "",
        "data": [
            {
                "topic": "parse-topic-low",
                "group_id": "parse-group-low",
                "total_lag": 100
            },
            {
                "topic": "parse-topic-high",
                "group_id": "parse-group-high",
                "total_lag": 50
            }
        ]
    }


@pytest.fixture
def mock_successful_kafka_throughput_response():
    """模拟成功的Kafka吞吐量响应"""
    return {
        "code": 0,
        "message": "",
        "data": [
            {
                "topic": "parse-topic-low",
                "group_id": "parse-group-low",
                "throughput": 10.5
            },
            {
                "topic": "parse-topic-high",
                "group_id": "parse-group-high",
                "throughput": 25.8
            }
        ]
    }


@pytest.fixture
def sample_general_parse_res_request():
    """示例解析结果请求数据"""
    return {
        "token": "test-token-12345",
        "return_ks3_url": True,
        "parse_target": ["chunk", "summary"]
    }


@pytest.fixture
def sample_merge_table_request():
    """示例表格合并请求数据"""
    return {
        "content": [
            "表格行1: 列1 | 列2 | 列3",
            "表格行2: 数据1 | 数据2 | 数据3",
            "表格行3: 值1 | 值2 | 值3"
        ]
    }


@pytest.fixture
def mock_successful_merge_table_response():
    """模拟成功的表格合并响应"""
    return {
        "code": 0,
        "message": "success",
        "data": {
            "content": [
                "合并后的表格行1: 列1 | 列2 | 列3",
                "合并后的表格行2: 数据1 | 数据2 | 数据3"
            ]
        }
    }


@pytest.fixture
def mock_successful_version_response():
    """模拟成功的版本响应"""
    return {
        "code": 0,
        "message": "success",
        "data": {
            "version": "v1.0.0-test"
        }
    }
